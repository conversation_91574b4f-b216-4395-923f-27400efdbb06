# pip install streamlit requests pandas numpy
import requests
import pandas as pd
import numpy as np
import datetime as dt
import streamlit as st

# Binance API base URL
BINANCE_BASE = "https://fapi.binance.com/futures/data"

# Sembol listesi
SYMBOLS = ["BTCUSDT", "ETHUSDT", "SOLUSDT", "BNBUSDT", "XRPUSDT", "DOGEUSDT", "ADAUSDT"]

# Streamlit sayfa konfigürasyonu
st.set_page_config(page_title="Long/Short Radar", layout="wide")
st.title("Long/Short Radar (Binance Futures)")

# Kullanıcı girişleri
symbol = st.selectbox("Sembol", SYMBOLS, index=0)
timeframe_choice = st.radio("Zaman Penceresi", ["12h", "24h", "1w", "1mo"], horizontal=True)

def get_since_timestamp(window):
    """Seçilen zaman penceresine göre başlangıç timestamp'ini hesapla"""
    now = int(dt.datetime.utcnow().timestamp() * 1000)
    hours_map = {"12h": 12, "24h": 24, "1w": 7*24, "1mo": 30*24}
    hours = hours_map[window]
    return now - hours * 60 * 60 * 1000

def fetch_long_short_ratio(endpoint, symbol, window):
    """Binance API'den long/short ratio verilerini çek"""
    try:
        params = {
            "symbol": symbol,
            "period": "5m",
            "limit": 500
        }
        url = f"{BINANCE_BASE}/{endpoint}"

        st.write(f"🔍 API çağrısı: {url}")
        st.write(f"📊 Parametreler: {params}")

        response = requests.get(url, params=params, timeout=15)

        st.write(f"📡 Response Status: {response.status_code}")

        if response.status_code != 200:
            st.error(f"API Hatası: {response.status_code} - {response.text}")
            return pd.DataFrame()

        data = response.json()

        if not data:
            st.warning("API'den boş veri döndü")
            return pd.DataFrame()

        st.write(f"✅ {len(data)} adet veri alındı")

        df = pd.DataFrame(data)

        # Veri yapısını kontrol et
        st.write("📋 Veri sütunları:", list(df.columns))

        df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")

        # Seçilen zaman penceresine göre filtrele
        since_timestamp = get_since_timestamp(window)
        df = df[df["timestamp"].astype("int64") // 10**6 >= since_timestamp]

        # Long/Short oranlarını hesapla
        df["ratio"] = df["longShortRatio"].astype(float)
        df["long_pct"] = df["ratio"] / (1 + df["ratio"])
        df["short_pct"] = 1 - df["long_pct"]

        return df

    except requests.exceptions.RequestException as e:
        st.error(f"🌐 Bağlantı hatası: {str(e)}")
        return pd.DataFrame()
    except KeyError as e:
        st.error(f"📊 Veri yapısı hatası: {str(e)} - API response yapısı beklenenden farklı")
        return pd.DataFrame()
    except Exception as e:
        st.error(f"❌ Genel hata: {str(e)}")
        return pd.DataFrame()

# Tab'ları oluştur
tabs = st.tabs(["Tüm Hesaplar", "Top Trader (Hesap)", "Top Trader (Pozisyon)"])
endpoints = [
    "globalLongShortAccountRatio",
    "topLongShortAccountRatio", 
    "topLongShortPositionRatio"
]

summaries = []

for i, endpoint in enumerate(endpoints):
    with tabs[i]:
        df = fetch_long_short_ratio(endpoint, symbol, timeframe_choice)
        
        if df.empty:
            st.warning("Bu endpoint için veri bulunamadı.")
            continue
        
        # Ağırlıklı ortalama hesapla (medyan + EMA karışımı)
        long_median = df["long_pct"].median()
        long_ema = df["long_pct"].ewm(span=max(1, round(len(df)/3))).mean().iloc[-1]
        long_pct = float((long_median + long_ema) / 2)
        short_pct = 1 - long_pct
        
        # Dominance belirleme
        if long_pct > 0.53:
            dominance = "LONG baskın"
        elif long_pct < 0.47:
            dominance = "SHORT baskın"
        else:
            dominance = "Nötr"
        
        # Metrikleri göster
        st.metric(
            "Dominance", 
            dominance, 
            delta=f"Long %{long_pct*100:.1f} / Short %{short_pct*100:.1f}"
        )
        
        # Grafik göster
        chart_data = df.set_index("timestamp")[["long_pct", "short_pct"]]
        st.line_chart(chart_data)
        
        # Son 10 veriyi tablo olarak göster
        st.subheader("Son Veriler")
        display_df = df[["timestamp", "long_pct", "short_pct"]].tail(10).copy()
        display_df["long_pct"] = display_df["long_pct"].apply(lambda x: f"%{x*100:.1f}")
        display_df["short_pct"] = display_df["short_pct"].apply(lambda x: f"%{x*100:.1f}")
        st.dataframe(display_df)
        
        summaries.append((endpoint, long_pct, short_pct, dominance))

# Özet bölümü
st.subheader("Özet")
endpoint_labels = {
    "globalLongShortAccountRatio": "Tüm Hesaplar",
    "topLongShortAccountRatio": "Top Trader (Hesap)",
    "topLongShortPositionRatio": "Top Trader (Pozisyon)"
}

for endpoint_name, long_pct, short_pct, dominance in summaries:
    label = endpoint_labels[endpoint_name]
    st.write(f"**{label}** – {dominance} (Long %{long_pct*100:.1f} / Short %{short_pct*100:.1f})")

st.caption("Not: 53% üstü LONG, 47% altı SHORT baskın olarak değerlendirilir.")

# Çalıştırma talimatları
st.sidebar.markdown("""
### Nasıl Çalıştırılır:
1. Terminal'de: `pip install streamlit requests pandas numpy`
2. Sonra: `streamlit run long_short_radar.py`
3. Tarayıcıda otomatik açılacak
""")
